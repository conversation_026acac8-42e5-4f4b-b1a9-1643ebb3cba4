# 範例 GitHub Action workflow 檔案
# 展示如何定義 workflow_dispatch 輸入參數

name: Example Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      version:
        description: 'Version to deploy'
        required: false
        default: 'latest'
        type: string
      debug:
        description: 'Enable debug mode'
        required: false
        default: false
        type: boolean
      platform:
        description: 'Target platform'
        required: false
        default: 'linux'
        type: choice
        options:
          - linux
          - windows
          - macos

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Display inputs
        run: |
          echo "Environment: ${{ inputs.environment }}"
          echo "Version: ${{ inputs.version }}"
          echo "Debug: ${{ inputs.debug }}"
          echo "Platform: ${{ inputs.platform }}"

      - name: Deploy
        run: |
          echo "Deploying version ${{ inputs.version }} to ${{ inputs.environment }}"
          if [[ "${{ inputs.debug }}" == "true" ]]; then
            echo "Debug mode enabled"
          fi
          echo "Target platform: ${{ inputs.platform }}"
