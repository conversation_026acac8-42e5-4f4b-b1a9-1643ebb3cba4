# GitHub Action 觸發器 - 自訂參數功能說明

## 概述

`trigger-github-action.sh` 腳本現在支援傳遞自訂參數給 GitHub Action，讓您可以在觸發 workflow 時動態設定參數值。

## 前置條件

1. **Workflow 必須支援 `workflow_dispatch`**
   ```yaml
   on:
     workflow_dispatch:
       inputs:
         environment:
           description: 'Deployment environment'
           required: true
           default: 'staging'
         version:
           description: 'Version to deploy'
           required: false
   ```

2. **參數必須在 workflow 檔案中預先定義**
   - 所有要傳遞的參數都必須在 `workflow_dispatch.inputs` 區段中定義
   - 未定義的參數會被 GitHub 忽略

## 使用方法

### 命令列模式

```bash
# 基本語法
bash trigger-github-action.sh <repo> <branch> <workflow.yml> [key=value ...]

# 範例 1: 單一參數
bash trigger-github-action.sh myuser/myrepo main deploy.yml environment=production

# 範例 2: 多個參數
bash trigger-github-action.sh myuser/myrepo main deploy.yml environment=production version=v1.2.3 debug=true

# 範例 3: 複雜參數
bash trigger-github-action.sh company/project feature/test test.yml test_suite=integration platform=linux arch=x64
```

### 互動模式

```bash
bash trigger-github-action.sh
```

在互動模式中，腳本會：
1. 要求您輸入 repository、branch 和 workflow ID
2. 提示您輸入自訂參數（可選）
3. 支援逐一輸入參數，輸入 'done' 完成設定

## 參數格式規則

1. **格式**: `key=value`
2. **Key 規則**:
   - 只能包含字母、數字和底線
   - 必須以字母或底線開頭
   - 範例: `environment`, `test_suite`, `_debug`

3. **Value 規則**:
   - 可以包含任何字符
   - 如果包含空格，在命令列中需要用引號包圍
   - 範例: `message="Hello World"`, `path="/home/<USER>/app"`

## 實際範例

假設您有以下 workflow 定義：

```yaml
on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
      version:
        description: 'Version to deploy'
        required: false
        default: 'latest'
      debug:
        description: 'Enable debug mode'
        required: false
        default: false
        type: boolean
```

### 觸發範例

```bash
# 部署到 production 環境
bash trigger-github-action.sh myuser/app main deploy.yml environment=production

# 部署特定版本並啟用 debug
bash trigger-github-action.sh myuser/app main deploy.yml environment=staging version=v2.1.0 debug=true

# 使用所有預設值（不傳遞任何參數）
bash trigger-github-action.sh myuser/app main deploy.yml
```

## 錯誤處理

腳本會驗證參數格式：

```bash
# ✅ 正確格式
environment=production
version=v1.2.3
debug=true

# ❌ 錯誤格式
environment production  # 缺少等號
123version=test         # key 不能以數字開頭
env-name=test          # key 不能包含連字號
```

## 注意事項

1. **參數必須預先定義**: 只有在 workflow 檔案中 `inputs` 區段定義的參數才會被接受
2. **型別轉換**: GitHub 會根據 workflow 定義自動轉換參數型別
3. **必填參數**: 如果 workflow 定義了必填參數但未提供，GitHub 會使用預設值或報錯
4. **大小寫敏感**: 參數名稱區分大小寫

## 除錯技巧

1. **檢查 workflow 定義**: 確保參數在 `workflow_dispatch.inputs` 中正確定義
2. **查看執行日誌**: 使用 `gh run view <run-id>` 查看詳細執行日誌
3. **測試參數**: 先在 GitHub 網頁介面手動觸發 workflow 測試參數

## 相關指令

```bash
# 查看 repository 中的所有 workflows
gh workflow list --repo owner/repo

# 查看特定 workflow 的詳細資訊
gh workflow view workflow-id --repo owner/repo

# 查看最近的 workflow runs
gh run list --repo owner/repo --workflow workflow-id
```
