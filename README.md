# GitHub Action 觸發器腳本

一個在 Mac 上執行的 shell 腳本，可以透過 GitHub CLI 觸發 GitHub Action 並即時監控執行狀態。支援命令列參數和互動式兩種使用模式。

## 🚀 功能特色

- ✅ **雙模式支援** - 支援命令列參數和互動式輸入兩種模式
- ✅ **智能檔案匹配** - 根據 workflow 檔案名稱自動查找對應的 workflow
- ✅ **即時監控** - 每 5 秒自動檢查執行狀態
- ✅ **彩色輸出** - 不同狀態使用不同顏色，易於識別
- ✅ **錯誤處理** - 完整的錯誤檢查和提示
- ✅ **支援自訂分支** - 可指定任何分支執行
- ✅ **自動化友好** - 適合整合到 CI/CD 流程中

## 📋 前置需求

### 1. 安裝 GitHub CLI
```bash
brew install gh
```

### 2. 登入 GitHub
```bash
gh auth login
```
按照提示完成 GitHub 帳號登入。

### 3. 安裝 jq（JSON 處理工具）
```bash
brew install jq
```

## 🎯 快速開始

### 命令列模式（推薦）
```bash
# 基本使用
bash trigger-github-action.sh myuser/myrepo main ci.yml

# 部署到生產環境
bash trigger-github-action.sh myuser/myrepo main deploy.yml

# 在開發分支執行測試
bash trigger-github-action.sh myuser/myrepo develop test.yml
```

## 🛠 安裝與設定

1. **下載腳本**
   ```bash
   # 如果是從 git repository 下載
   git clone <repository-url>
   cd <repository-directory>
   
   # 或直接下載腳本檔案
   curl -O <script-url>/trigger-github-action.sh
   ```

2. **給予執行權限**
   ```bash
   chmod +x trigger-github-action.sh
   ```

## 📖 使用方法

### 方法一：命令列模式（推薦）
```bash
bash trigger-github-action.sh <repo> <branch> <action.yml>
```

**參數說明：**
- `repo` - GitHub repository（格式：owner/repo）
- `branch` - 要執行的分支名稱
- `action.yml` - workflow 檔案名稱（例如：ci.yml, deploy.yml）

**範例：**
```bash
# 在 main 分支執行 CI
bash trigger-github-action.sh myuser/myrepo main ci.yml

# 在 develop 分支執行部署
bash trigger-github-action.sh myuser/myrepo develop deploy.yml

# 在 feature 分支執行測試
bash trigger-github-action.sh company/project feature/new-feature test.yml
```

### 方法二：互動模式
```bash
bash trigger-github-action.sh
```
腳本會引導您逐步輸入所需資訊。

### 顯示說明
```bash
bash trigger-github-action.sh --help
```

## 🎯 使用步驟

### 命令列模式步驟
1. **直接執行**
   ```bash
   bash trigger-github-action.sh myuser/myrepo main ci.yml
   ```

2. **自動執行**
   腳本會自動：
   - 驗證參數
   - 查找對應的 workflow
   - 觸發 GitHub Action
   - 開始監控執行狀態

### 互動模式步驟
1. **執行腳本**
   ```bash
   bash trigger-github-action.sh
   ```

2. **輸入 Repository**
   ```
   請輸入 repository (格式: owner/repo): myuser/myproject
   ```

3. **輸入 Branch**
   ```
   請輸入 branch (預設: main): develop
   ```

4. **選擇 Workflow**
   ```
   可用的 workflows:
     ID: 12345678 - CI/CD Pipeline (.github/workflows/ci.yml)
     ID: 87654321 - Deploy to Production (.github/workflows/deploy.yml)

   請輸入要執行的 workflow ID: 12345678
   ```

5. **監控執行**
   ```
   [2025-07-01 14:30:15] ⏳ GitHub Action 執行中...
   [2025-07-01 14:30:20] ⏳ GitHub Action 執行中...
   [2025-07-01 14:30:25] ✓ GitHub Action 執行成功！
   ```

## 📊 狀態說明

| 狀態 | 圖示 | 說明 |
|------|------|------|
| 排隊中 | ⏸ | GitHub Action 正在等待執行 |
| 執行中 | ⏳ | GitHub Action 正在執行 |
| 成功 | ✓ | GitHub Action 執行成功 |
| 失敗 | ✗ | GitHub Action 執行失敗 |
| 取消 | ⚠ | GitHub Action 已被取消 |

## 🔧 進階使用

### 建立別名快速執行
```bash
# 在 ~/.bashrc 或 ~/.zshrc 中加入
alias trigger-ci="bash /path/to/trigger-github-action.sh"
alias deploy-prod="bash /path/to/trigger-github-action.sh myuser/myrepo main deploy.yml"
alias run-tests="bash /path/to/trigger-github-action.sh myuser/myrepo develop test.yml"
```

### 整合到其他腳本
```bash
#!/bin/bash
# 部署腳本範例

echo "開始部署流程..."

# 執行測試
bash trigger-github-action.sh myuser/myrepo main test.yml

# 如果測試通過，執行部署
if [ $? -eq 0 ]; then
    echo "測試通過，開始部署..."
    bash trigger-github-action.sh myuser/myrepo main deploy.yml
else
    echo "測試失敗，取消部署"
    exit 1
fi
```

### 查看執行歷史
```bash
# 查看特定 repository 的執行歷史
gh run list --repo owner/repo

# 查看特定 workflow 的執行歷史
gh run list --repo owner/repo --workflow="workflow-name"

# 查看最近 10 次執行
gh run list --repo owner/repo --limit 10
```

## ❗ 注意事項

1. **權限要求** - 確保您有目標 repository 的 Actions 執行權限
2. **檔案名稱匹配** - workflow 檔案名稱需要完整匹配（包含副檔名）
3. **網路連線** - 需要穩定的網路連線來監控狀態
4. **停止監控** - 按 `Ctrl+C` 可停止監控，但不會取消已觸發的 GitHub Action
5. **API 限制** - GitHub API 有使用限制，頻繁查詢可能會受到限制
6. **分支存在** - 確保指定的分支在 repository 中存在

## 🐛 常見問題

### Q: 出現 "GitHub CLI (gh) 未安裝" 錯誤
**A:** 請先安裝 GitHub CLI：`brew install gh`

### Q: 出現 "尚未登入 GitHub" 錯誤
**A:** 請先登入 GitHub：`gh auth login`

### Q: 出現 "參數數量不正確" 錯誤
**A:** 檢查命令格式：
```bash
# 正確格式
bash trigger-github-action.sh myuser/myrepo main ci.yml

# 或使用互動模式
bash trigger-github-action.sh
```

### Q: 找不到指定的 workflow 檔案
**A:** 檢查：
- 檔案名稱是否正確（包含副檔名，如 `.yml` 或 `.yaml`）
- 檔案是否存在於 `.github/workflows/` 目錄中
- 檔案名稱大小寫是否正確

### Q: 找不到可用的 workflows
**A:** 檢查：
- Repository 名稱是否正確（格式：owner/repo）
- 您是否有該 repository 的存取權限
- Repository 中是否有啟用的 GitHub Actions
- Workflow 檔案是否處於 active 狀態

### Q: 無法獲取 workflow run ID
**A:** 可能原因：
- 網路連線問題
- GitHub API 暫時無回應
- Workflow 觸發失敗
- 指定的分支不存在

### Q: 腳本執行權限問題
**A:** 確保腳本有執行權限：
```bash
chmod +x trigger-github-action.sh
```

## 📝 版本資訊

- **版本**: 2.0
- **作者**: AI Assistant
- **日期**: 2025-07-01
- **相容性**: macOS with Bash

### 版本更新記錄

#### v2.0 (2025-07-01)
- ✅ 新增命令列參數支援
- ✅ 智能 workflow 檔案名稱匹配
- ✅ 保留互動模式向後相容
- ✅ 改進錯誤處理和驗證
- ✅ 更新使用說明和範例

#### v1.0 (2025-07-01)
- ✅ 基本互動式功能
- ✅ GitHub CLI 整合
- ✅ 即時狀態監控
- ✅ 彩色輸出支援

## 🤝 貢獻

歡迎提交 Issue 或 Pull Request 來改善這個腳本！

## 📄 授權

此腳本採用 MIT 授權條款。
