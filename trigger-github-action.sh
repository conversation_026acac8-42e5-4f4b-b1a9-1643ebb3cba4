#!/bin/bash

# ============================================================================
# GitHub Action 觸發器腳本
# ============================================================================
#
# 描述：
#   這個腳本可以透過 GitHub CLI 觸發指定的 GitHub Action，
#   並即時監控執行狀態直到完成。
#
# 使用方法：
#   bash trigger-github-action.sh <repo> <branch> <action.yml> [key=value ...]
#   bash trigger-github-action.sh                                               # 互動模式
#
# 參數說明：
#   repo        - GitHub repository (格式: owner/repo)
#   branch      - 要執行的分支名稱
#   action.yml  - workflow 檔案名稱 (例如: ci.yml, deploy.yml)
#   key=value   - 自訂參數 (可選，可傳遞多個)
#
# 前置需求：
#   1. 安裝 GitHub CLI: brew install gh
#   2. 登入 GitHub: gh auth login
#   3. 安裝 jq: brew install jq
#
# 功能特色：
#   - 支援命令列參數和互動式輸入
#   - 支援傳遞自訂參數給 GitHub Action
#   - 自動列出可用的 workflows
#   - 即時監控執行狀態（每 5 秒更新）
#   - 彩色輸出，易於閱讀
#   - 完整的錯誤處理
#
# 範例：
#   bash trigger-github-action.sh myuser/myrepo main ci.yml
#   bash trigger-github-action.sh myuser/myrepo develop deploy.yml environment=production version=v1.2.3
#   bash trigger-github-action.sh myuser/myrepo main test.yml test_suite=integration debug=true
#
# 作者：AI Assistant
# 版本：2.0
# 日期：2025-07-01
#
# ============================================================================

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 檢查是否安裝了 GitHub CLI
check_gh_cli() {
    if ! command -v gh &> /dev/null; then
        echo -e "${RED}錯誤: GitHub CLI (gh) 未安裝${NC}"
        echo "請先安裝 GitHub CLI: https://cli.github.com/"
        exit 1
    fi
}

# 檢查是否已登入 GitHub
check_gh_auth() {
    if ! gh auth status &> /dev/null; then
        echo -e "${RED}錯誤: 尚未登入 GitHub${NC}"
        echo "請先執行: gh auth login"
        exit 1
    fi
}

# 解析命令列參數
parse_arguments() {
    if [[ $# -ge 3 ]]; then
        # 命令列模式
        REPO="$1"
        BRANCH="$2"
        WORKFLOW_FILE="$3"

        # 解析自訂參數 (從第4個參數開始)
        shift 3
        CUSTOM_PARAMS=()
        while [[ $# -gt 0 ]]; do
            if [[ "$1" =~ ^[a-zA-Z_][a-zA-Z0-9_]*=.* ]]; then
                CUSTOM_PARAMS+=("$1")
            else
                echo -e "${RED}錯誤: 無效的參數格式 '$1'${NC}"
                echo "參數格式應為: key=value"
                exit 1
            fi
            shift
        done

        echo -e "${BLUE}=== 命令列模式 ===${NC}"
        echo "Repository: $REPO"
        echo "Branch: $BRANCH"
        echo "Workflow 檔案: $WORKFLOW_FILE"
        if [[ ${#CUSTOM_PARAMS[@]} -gt 0 ]]; then
            echo "自訂參數:"
            for param in "${CUSTOM_PARAMS[@]}"; do
                echo "  $param"
            done
        fi
        echo

        # 驗證參數
        if [[ -z "$REPO" ]]; then
            echo -e "${RED}錯誤: Repository 不能為空${NC}"
            exit 1
        fi

        if [[ -z "$BRANCH" ]]; then
            echo -e "${RED}錯誤: Branch 不能為空${NC}"
            exit 1
        fi

        if [[ -z "$WORKFLOW_FILE" ]]; then
            echo -e "${RED}錯誤: Workflow 檔案不能為空${NC}"
            exit 1
        fi

        # 根據檔案名稱獲取 workflow ID
        get_workflow_id_by_file

    elif [[ $# -eq 0 ]]; then
        # 互動模式
        get_user_input_interactive
    else
        echo -e "${RED}錯誤: 參數數量不正確${NC}"
        echo "使用方法:"
        echo "  bash trigger-github-action.sh <repo> <branch> <action.yml> [key=value ...]"
        echo "  bash trigger-github-action.sh  # 互動模式"
        exit 1
    fi
}

# 根據檔案名稱獲取 workflow ID
get_workflow_id_by_file() {
    echo -e "${YELLOW}正在查找 workflow: $WORKFLOW_FILE${NC}"

    # 獲取所有 workflows 並根據檔案名稱查找
    WORKFLOW_DATA=$(gh workflow list --repo "$REPO" --json name,id,state,path | jq -r --arg file "$WORKFLOW_FILE" '.[] | select(.state == "active" and (.path | endswith($file))) | "\(.id):\(.name):\(.path)"')

    if [[ -z "$WORKFLOW_DATA" ]]; then
        echo -e "${RED}錯誤: 找不到檔案名稱為 '$WORKFLOW_FILE' 的 workflow${NC}"
        echo -e "${YELLOW}可用的 workflows:${NC}"
        gh workflow list --repo "$REPO" --json name,path,state | jq -r '.[] | select(.state == "active") | "  \(.path) - \(.name)"'
        exit 1
    fi

    # 解析 workflow 資料
    IFS=':' read -r WORKFLOW_ID WORKFLOW_NAME WORKFLOW_PATH <<< "$WORKFLOW_DATA"

    echo -e "${GREEN}找到 workflow:${NC}"
    echo "  ID: $WORKFLOW_ID"
    echo "  名稱: $WORKFLOW_NAME"
    echo "  路徑: $WORKFLOW_PATH"
    echo
}

# 互動式獲取用戶輸入
get_user_input_interactive() {
    echo -e "${BLUE}=== 互動模式 ===${NC}"
    echo

    # 獲取 repository
    read -p "請輸入 repository (格式: owner/repo): " REPO
    if [[ -z "$REPO" ]]; then
        echo -e "${RED}錯誤: Repository 不能為空${NC}"
        exit 1
    fi

    # 獲取 branch
    read -p "請輸入 branch (預設: main): " BRANCH
    if [[ -z "$BRANCH" ]]; then
        BRANCH="Dev"
    fi

    # 獲取可用的 workflows
    echo -e "${YELLOW}正在獲取可用的 workflows...${NC}"
    WORKFLOWS=$(gh workflow list --repo "$REPO" --json name,id,state,path | jq -r '.[] | select(.state == "active") | "\(.id):\(.name):\(.path)"')

    if [[ -z "$WORKFLOWS" ]]; then
        echo -e "${RED}錯誤: 在 repository $REPO 中找不到可用的 workflows${NC}"
        exit 1
    fi

    echo -e "${BLUE}可用的 workflows:${NC}"
    echo "$WORKFLOWS" | while IFS=':' read -r id name path; do
        echo "  ID: $id - $name ($path)"
    done
    echo

    read -p "請輸入要執行的 workflow ID: " WORKFLOW_ID
    if [[ -z "$WORKFLOW_ID" ]]; then
        echo -e "${RED}錯誤: Workflow ID 不能為空${NC}"
        exit 1
    fi

    # 獲取自訂參數
    echo
    echo -e "${BLUE}自訂參數設定 (可選):${NC}"
    echo "格式: key=value (直接按 Enter 跳過)"
    echo "輸入 'done' 完成參數設定"
    echo

    CUSTOM_PARAMS=()
    while true; do
        read -p "請輸入參數 (或 'done' 完成): " param
        if [[ "$param" == "done" || -z "$param" ]]; then
            break
        elif [[ "$param" =~ ^[a-zA-Z_][a-zA-Z0-9_]*=.* ]]; then
            CUSTOM_PARAMS+=("$param")
            echo "已新增參數: $param"
        else
            echo -e "${YELLOW}警告: 無效的參數格式，請使用 key=value 格式${NC}"
        fi
    done

    if [[ ${#CUSTOM_PARAMS[@]} -gt 0 ]]; then
        echo -e "${GREEN}已設定的自訂參數:${NC}"
        for param in "${CUSTOM_PARAMS[@]}"; do
            echo "  $param"
        done
        echo
    fi
}

# 觸發 GitHub Action
trigger_action() {
    echo -e "${YELLOW}正在觸發 GitHub Action...${NC}"
    echo "Repository: $REPO"
    echo "Branch: $BRANCH"
    if [[ -n "$WORKFLOW_FILE" ]]; then
        echo "Workflow 檔案: $WORKFLOW_FILE"
    fi
    echo "Workflow ID: $WORKFLOW_ID"
    if [[ ${#CUSTOM_PARAMS[@]} -gt 0 ]]; then
        echo "自訂參數:"
        for param in "${CUSTOM_PARAMS[@]}"; do
            echo "  $param"
        done
    fi
    echo

    # 建構 gh workflow run 指令
    CMD_ARGS=("workflow" "run" "$WORKFLOW_ID" "--repo" "$REPO" "--ref" "$BRANCH")

    # 新增自訂參數
    for param in "${CUSTOM_PARAMS[@]}"; do
        IFS='=' read -r key value <<< "$param"
        CMD_ARGS+=("-f" "$key=$value")
    done

    # 觸發 workflow
    if gh "${CMD_ARGS[@]}"; then
        echo -e "${GREEN}✓ GitHub Action 已成功觸發${NC}"
    else
        echo -e "${RED}✗ 觸發 GitHub Action 失敗${NC}"
        exit 1
    fi
}

# 獲取最新的 workflow run ID
get_latest_run_id() {
    echo -e "${YELLOW}正在獲取最新的 workflow run...${NC}"
    sleep 3  # 等待一下讓 GitHub 處理
    
    RUN_ID=$(gh run list --repo "$REPO" --workflow="$WORKFLOW_ID" --limit=1 --json databaseId --jq '.[0].databaseId')
    
    if [[ -z "$RUN_ID" || "$RUN_ID" == "null" ]]; then
        echo -e "${RED}錯誤: 無法獲取 workflow run ID${NC}"
        exit 1
    fi
    
    echo "Run ID: $RUN_ID"
}

# 監控 GitHub Action 執行狀態
monitor_action() {
    echo -e "${BLUE}開始監控 GitHub Action 執行狀態...${NC}"
    echo "每 5 秒檢查一次狀態，按 Ctrl+C 可停止監控"
    echo
    
    while true; do
        # 獲取當前狀態
        STATUS=$(gh run view "$RUN_ID" --repo "$REPO" --json status,conclusion --jq '.status')
        CONCLUSION=$(gh run view "$RUN_ID" --repo "$REPO" --json status,conclusion --jq '.conclusion')
        
        # 獲取當前時間
        CURRENT_TIME=$(date '+%Y-%m-%d %H:%M:%S')
        
        case "$STATUS" in
            "completed")
                case "$CONCLUSION" in
                    "success")
                        echo -e "${GREEN}[$CURRENT_TIME] ✓ GitHub Action 執行成功！${NC}"
                        ;;
                    "failure")
                        echo -e "${RED}[$CURRENT_TIME] ✗ GitHub Action 執行失敗${NC}"
                        ;;
                    "cancelled")
                        echo -e "${YELLOW}[$CURRENT_TIME] ⚠ GitHub Action 已取消${NC}"
                        ;;
                    *)
                        echo -e "${YELLOW}[$CURRENT_TIME] ⚠ GitHub Action 完成，結果: $CONCLUSION${NC}"
                        ;;
                esac
                
                echo
                echo -e "${BLUE}查看詳細結果:${NC}"
                echo "gh run view $RUN_ID --repo $REPO"
                echo "或訪問: https://github.com/$REPO/actions/runs/$RUN_ID"
                break
                ;;
            "in_progress")
                echo -e "${BLUE}[$CURRENT_TIME] ⏳ GitHub Action 執行中...${NC}"
                ;;
            "queued")
                echo -e "${YELLOW}[$CURRENT_TIME] ⏸ GitHub Action 排隊中...${NC}"
                ;;
            *)
                echo -e "${YELLOW}[$CURRENT_TIME] 狀態: $STATUS${NC}"
                ;;
        esac
        
        # 如果已完成則退出循環
        if [[ "$STATUS" == "completed" ]]; then
            break
        fi
        
        # 等待 5 秒
        sleep 5
    done
}

# 顯示使用說明
show_help() {
    echo -e "${BLUE}GitHub Action 觸發器腳本 - 使用說明${NC}"
    echo "=============================================="
    echo
    echo -e "${GREEN}描述：${NC}"
    echo "  透過 GitHub CLI 觸發指定的 GitHub Action 並即時監控執行狀態"
    echo
    echo -e "${GREEN}使用方法：${NC}"
    echo "  bash trigger-github-action.sh <repo> <branch> <action.yml> [key=value ...]  # 命令列模式"
    echo "  bash trigger-github-action.sh                                               # 互動模式"
    echo "  bash trigger-github-action.sh -h|--help                                     # 顯示說明"
    echo
    echo -e "${GREEN}參數說明：${NC}"
    echo "  repo        - GitHub repository (格式: owner/repo)"
    echo "  branch      - 要執行的分支名稱"
    echo "  action.yml  - workflow 檔案名稱 (例如: ci.yml, deploy.yml)"
    echo "  key=value   - 自訂參數 (可選，可傳遞多個)"
    echo
    echo -e "${GREEN}前置需求：${NC}"
    echo "  1. 安裝 GitHub CLI: brew install gh"
    echo "  2. 登入 GitHub: gh auth login"
    echo "  3. 安裝 jq: brew install jq"
    echo
    echo -e "${GREEN}範例：${NC}"
    echo "  # 命令列模式 - 基本用法"
    echo "  bash trigger-github-action.sh myuser/myrepo main ci.yml"
    echo "  bash trigger-github-action.sh myuser/myrepo develop deploy.yml"
    echo
    echo "  # 命令列模式 - 帶自訂參數"
    echo "  bash trigger-github-action.sh myuser/myrepo main deploy.yml environment=production version=v1.2.3"
    echo "  bash trigger-github-action.sh company/project feature/test test.yml test_suite=integration debug=true"
    echo "  bash trigger-github-action.sh myuser/app main build.yml platform=linux arch=x64 release=true"
    echo
    echo "  # 互動模式"
    echo "  bash trigger-github-action.sh"
    echo
    echo -e "${GREEN}功能特色：${NC}"
    echo "  - 支援命令列參數和互動式輸入兩種模式"
    echo "  - 支援傳遞自訂參數給 GitHub Action (workflow_dispatch inputs)"
    echo "  - 自動根據檔案名稱查找對應的 workflow"
    echo "  - 即時監控執行狀態（每 5 秒更新）"
    echo "  - 彩色輸出，易於識別不同狀態"
    echo "  - 完整的錯誤處理和驗證"
    echo
    echo -e "${GREEN}注意事項：${NC}"
    echo "  - 確保您有該 repository 的 Actions 執行權限"
    echo "  - workflow 檔案名稱需要完整匹配（包含副檔名）"
    echo "  - 自訂參數必須在 workflow 檔案中的 workflow_dispatch.inputs 區段預先定義"
    echo "  - 參數格式為 key=value，key 只能包含字母、數字和底線"
    echo "  - 可隨時按 Ctrl+C 停止監控"
    echo "  - 停止監控不會取消已觸發的 GitHub Action"
    echo
}

# 主函數
main() {
    # 檢查是否要顯示說明
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then
        show_help
        exit 0
    fi

    echo -e "${GREEN}GitHub Action 觸發器腳本 v2.0${NC}"
    echo "======================================="
    echo

    # 檢查必要條件
    check_gh_cli
    check_gh_auth

    # 解析參數並獲取輸入
    parse_arguments "$@"

    # 觸發 Action
    trigger_action

    # 獲取 Run ID
    get_latest_run_id

    # 監控執行狀態
    monitor_action
}

# 執行主函數
main "$@"
